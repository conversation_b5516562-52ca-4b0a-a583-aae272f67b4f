{"name": "com.liteninja.unityinject", "version": "1.0.0", "displayName": "Ludo UnityInject", "description": "A lightweight, flexible dependency injection framework for Unity projects", "unity": "6000.0", "documentationUrl": "https://github.com/sponticelli/Ludo-UnityInject/blob/main/Assets/Ludo/Ludo-UnityInject/README.md", "changelogUrl": "https://github.com/sponticelli/Ludo-UnityInject/blob/main/CHANGELOG.md", "licensesUrl": "https://github.com/sponticelli/Ludo-UnityInject/blob/main/LICENSE", "dependencies": {"com.unity.editorcoroutines": "1.0.0"}, "keywords": ["dependency injection", "di", "ioc", "unity", "framework"], "author": {"name": "<PERSON><PERSON>"}}