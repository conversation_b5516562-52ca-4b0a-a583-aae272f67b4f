# UnityInject Documentation

Welcome to the UnityInject documentation. This directory contains comprehensive guides and reference materials for using the UnityInject dependency injection system in your Unity projects.

## Documentation Files

- [**UnityInject-API-Documentation.md**](./UnityInject-API-Documentation.md): Detailed documentation of the core interfaces and classes in the UnityInject system.
- [**UnityInject-Usage-Guide.md**](./UnityInject-Usage-Guide.md): Comprehensive guide with examples for using UnityInject in your projects.
- [**UnityInject-Patterns-AntiPatterns.md**](./UnityInject-Patterns-AntiPatterns.md): Recommended patterns and anti-patterns to follow when using UnityInject.
- [**UnityInject-Performance-Guide.md**](./UnityInject-Performance-Guide.md): Tips and best practices for optimizing performance with UnityInject.
- [**UnityInject-Testing-Guide.md**](./UnityInject-Testing-Guide.md): Strategies for testing code that uses UnityInject.

## Getting Started

If you're new to UnityInject, we recommend starting with the [Usage Guide](UnityInject-Usage-Guide.md), which provides a step-by-step introduction to the system.

For a quick reference of the API, see the [API Documentation](UnityInject-API-Documentation.md).

## Additional Resources

- The original README file in the parent directory provides a concise overview of the system.
- The XML documentation in the code itself provides detailed information about specific methods and classes.

## Contributing to Documentation

If you find any issues or have suggestions for improving this documentation, please contact the development team.