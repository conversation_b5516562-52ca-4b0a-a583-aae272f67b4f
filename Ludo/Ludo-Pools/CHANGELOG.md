# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.0.0] - 2023-05-11

### Added
- Initial release of Ludo Pools
- Core pooling functionality with PoolManager
- PooledObject component for tracking pooled objects
- ReturnToPoolAfterDelay component for automatic object return
- ReturnToPoolAction component for event-based object return
- GameObject extensions for easier pool interaction
